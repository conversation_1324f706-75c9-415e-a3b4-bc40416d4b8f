package com.androidmarket.pdfcreator.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import java.util.List;

import com.androidmarket.pdfcreator.R;
import com.androidmarket.pdfcreator.interfaces.OnItemClickListener;
import com.androidmarket.pdfcreator.pdfModel.BrushItem;

public class AdapterBrushItem extends RecyclerView.Adapter<AdapterBrushItem.BrushItemViewHolder> {

    private final Context mContext;
    private final OnItemClickListener mOnItemClickListener;
    private final List<BrushItem> mBrushItems;

    public AdapterBrushItem(Context context,
                            OnItemClickListener onItemClickListener,
                            List<BrushItem> brushItems) {
        mBrushItems = brushItems;
        mOnItemClickListener = onItemClickListener;
        mContext = context;
    }

    @NonNull
    @Override
    public BrushItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View mView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.brush_color_item, parent, false);
        return new AdapterBrushItem.BrushItemViewHolder(mView);
    }

    @Override
    public void onBindViewHolder(@NonNull BrushItemViewHolder holder, int position) {
        int color = mBrushItems.get(position).getColor();
        if (position == mBrushItems.size() - 1)
            holder.doodleButton.setBackground(mContext.getResources().getDrawable(color));
        else
            holder.doodleButton.setBackgroundColor(mContext.getResources().getColor(color));
    }

    @Override
    public int getItemCount() {
        return mBrushItems.size();
    }

    public class BrushItemViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {

        Button doodleButton;

        BrushItemViewHolder(View itemView) {
            super(itemView);
            itemView.setOnClickListener(this);
        }

        @Override
        public void onClick(View view) {
            mOnItemClickListener.onItemClick(getAdapterPosition());
        }
    }
}
