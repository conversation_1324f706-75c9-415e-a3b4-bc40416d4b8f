package com.androidmarket.pdfcreator.util;

/**
 * Simplified <PERSON>ont to resolve compilation errors
 * This is a temporary solution until all dependencies are properly integrated
 */
public class Font {

    public enum FontFamily {
        HELVETICA,
        TIMES_ROMAN,
        COURIER,
        SYMBOL,
        ZAPFDINGBATS
    }

    public static class FontStyle {
        public static final int NORMAL = 0;
        public static final int BOLD = 1;
        public static final int ITALIC = 2;
        public static final int BOLDITALIC = 3;
    }

    private FontFamily fontFamily;
    private int style;
    private float size;

    public Font(FontFamily fontFamily, float size, int style) {
        this.fontFamily = fontFamily;
        this.size = size;
        this.style = style;
    }

    public FontFamily getFontFamily() {
        return fontFamily;
    }

    public void setFontFamily(FontFamily fontFamily) {
        this.fontFamily = fontFamily;
    }

    public int getStyle() {
        return style;
    }

    public void setStyle(int style) {
        this.style = style;
    }

    public float getSize() {
        return size;
    }

    public void setSize(float size) {
        this.size = size;
    }
}
