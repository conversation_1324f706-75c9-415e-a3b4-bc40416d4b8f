package com.androidmarket.pdfcreator.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Simplified HomeActivity to resolve compilation errors
 * This is a temporary solution until all dependencies are properly integrated
 */
public class HomeActivity extends AppCompatActivity implements View.OnClickListener {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // Simplified implementation
    }

    @Override
    public void onClick(View v) {
        // Simplified implementation - just start SecondActivity
        Intent intent = new Intent(HomeActivity.this, SecondActivity.class);
        intent.putExtra("fragment", "imgToPdf");
        startActivity(intent);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }

    public void Disappear(View view) {
        // Simplified implementation
    }
}
