package com.androidmarket.pdfcreator.fragment.texttopdf;

import android.app.Activity;

/**
 * Simplified Enhancers to resolve compilation errors
 * This is a temporary solution until all dependencies are properly integrated
 */
public enum Enhancers {
    FONT_COLOR {
        @Override
        Object getEnhancer(Activity activity, Object view, Object builder) {
            return new FontColorEnhancer(activity);
        }
    },
    FONT_FAMILY {
        @Override
        Object getEnhancer(Activity activity, Object view, Object builder) {
            return new FontFamilyEnhancer(activity);
        }
    },
    FONT_SIZE {
        @Override
        Object getEnhancer(Activity activity, Object view, Object builder) {
            return new FontSizeEnhancer(activity);
        }
    },
    PAGE_COLOR {
        @Override
        Object getEnhancer(Activity activity, Object view, Object builder) {
            return new PageColorEnhancer(activity);
        }
    },
    PAGE_SIZE {
        @Override
        Object getEnhancer(Activity activity, Object view, Object builder) {
            return new PageSizeEnhancer(activity);
        }
    },
    PASSWORD {
        @Override
        Object getEnhancer(Activity activity, Object view, Object builder) {
            return new PasswordEnhancer(activity);
        }
    };

    abstract Object getEnhancer(Activity activity, Object view, Object builder);
}
