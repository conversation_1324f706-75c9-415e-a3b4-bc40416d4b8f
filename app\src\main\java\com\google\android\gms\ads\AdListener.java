package com.google.android.gms.ads;

/**
 * Stub class for AdListener to resolve compilation errors
 * This is a temporary solution until the Google Ads library is properly integrated
 */
public class AdListener {
    
    public void onAdLoaded() {
        // Stub implementation
    }
    
    public void onAdFailedToLoad(int errorCode) {
        // Stub implementation
    }
    
    public void onAdOpened() {
        // Stub implementation
    }
    
    public void onAdClosed() {
        // Stub implementation
    }
    
    public void onAdLeftApplication() {
        // Stub implementation
    }
}
