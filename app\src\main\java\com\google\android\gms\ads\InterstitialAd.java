package com.google.android.gms.ads;

import android.content.Context;

/**
 * Stub class for InterstitialAd to resolve compilation errors
 * This is a temporary solution until the Google Ads library is properly integrated
 */
public class InterstitialAd {
    
    public InterstitialAd(Context context) {
        // Stub constructor
    }
    
    public void setAdUnitId(String adUnitId) {
        // Stub implementation
    }
    
    public void loadAd(AdRequest adRequest) {
        // Stub implementation
    }
    
    public boolean isLoaded() {
        return false;
    }
    
    public void show() {
        // Stub implementation
    }
    
    public void setAdListener(AdListener adListener) {
        // Stub implementation
    }
}
