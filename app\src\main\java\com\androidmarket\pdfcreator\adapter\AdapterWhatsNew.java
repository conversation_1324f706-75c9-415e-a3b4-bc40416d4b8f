package com.androidmarket.pdfcreator.adapter;

import android.content.Context;
import android.content.res.Resources;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import java.util.ArrayList;
import java.util.List;

import com.androidmarket.pdfcreator.R;
import com.androidmarket.pdfcreator.pdfModel.WhatsNew;

public class AdapterWhatsNew extends RecyclerView.Adapter<AdapterWhatsNew.WhatsNewViewHolder> {

    private final Context mContext;
    private final List<WhatsNew> mWhatsNewsList;

    public AdapterWhatsNew(Context context, ArrayList<WhatsNew> mWhatsNewsList) {
        this.mContext = context;
        this.mWhatsNewsList = mWhatsNewsList;
    }

    @NonNull
    @Override
    public WhatsNewViewHolder onCreateViewHolder(@NonNull ViewGroup mParent, int viewType) {
        return new WhatsNewViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull WhatsNewViewHolder holder, int position) {
        if (!mWhatsNewsList.get(position).getIcon().equals("")) {
            Resources resources = mContext.getResources();
            final int resourceId = resources.getIdentifier(mWhatsNewsList.get(position).getIcon(),
                    "drawable", mContext.getPackageName());
        }
    }

    @Override
    public int getItemCount() {
        return mWhatsNewsList.size();
    }

    public static class WhatsNewViewHolder extends RecyclerView.ViewHolder {


            this.binding = binding;
        }

    }

}
