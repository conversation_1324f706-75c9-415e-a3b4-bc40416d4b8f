package com.androidmarket.pdfcreator.adapter;

import android.content.Context;
import java.util.ArrayList;

/**
 * Simplified AdapterPreview to resolve compilation errors
 * This is a temporary solution until all dependencies are properly integrated
 */
public class AdapterPreview {

    private final Context mContext;
    private final ArrayList<String> mPreviewItems;

    public AdapterPreview(Context context, ArrayList<String> previewItems) {
        mContext = context;
        mPreviewItems = previewItems != null ? previewItems : new ArrayList<>();
    }

    public Object instantiateItem(Object view, int position) {
        // Simplified implementation
        return new Object();
    }

    public void destroyItem(Object collection, int position, Object view) {
        // Simplified implementation
    }

    public int getCount() {
        return mPreviewItems.size();
    }

    public boolean isViewFromObject(Object view, Object object) {
        return view == object;
    }

    public CharSequence getPageTitle(int position) {
        return "Image " + (position + 1) + " of " + mPreviewItems.size();
    }

    public void setData(ArrayList<String> images) {
        mPreviewItems.clear();
        if (images != null) {
            mPreviewItems.addAll(images);
        }
    }
}
