buildscript {
    ext.kotlin_version = "1.8.0"

    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:8.5.1"
        classpath "com.github.dcendents:android-maven-gradle-plugin:2.0"
        classpath "io.codearte.gradle.nexus:gradle-nexus-staging-plugin:0.22.0"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

android {
    namespace 'ja.burhanrashid52.photoeditor'
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core-ktx:1.9.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

// إزالة تعريف المهمة clean لتجنب التكرار
// task clean(type: Delete) {
//     delete rootProject.buildDir
// }
