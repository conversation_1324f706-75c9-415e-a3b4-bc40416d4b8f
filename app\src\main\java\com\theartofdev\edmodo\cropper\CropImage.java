package com.theartofdev.edmodo.cropper;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;

/**
 * Stub class for CropImage to resolve compilation errors
 * This is a temporary solution until the Image Cropper library is properly integrated
 */
public class CropImage {
    
    public static ActivityBuilder activity() {
        return new ActivityBuilder();
    }
    
    public static ActivityBuilder activity(Uri uri) {
        return new ActivityBuilder();
    }
    
    public static class ActivityBuilder {
        public ActivityBuilder setGuidelines(CropImageView.Guidelines guidelines) {
            return this;
        }
        
        public ActivityBuilder setAspectRatio(int aspectRatioX, int aspectRatioY) {
            return this;
        }
        
        public void start(Context context) {
            // Stub implementation
        }
        
        public void start(Activity activity) {
            // Stub implementation
        }
        
        public Intent getIntent(Context context) {
            return new Intent();
        }
    }
    
    public static class CropImageView {
        public enum Guidelines {
            OFF, ON_TOUCH, ON
        }
    }
}
