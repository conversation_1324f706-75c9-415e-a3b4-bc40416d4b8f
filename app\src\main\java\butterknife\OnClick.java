package butterknife;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Stub annotation for OnClick to resolve compilation errors
 * This is a temporary solution until ButterKnife is properly integrated or replaced
 */
@Retention(RUNTIME)
@Target(METHOD)
public @interface OnClick {
    int[] value() default { -1 };
}
