package com.androidmarket.pdfcreator.util;

import android.content.Context;

/**
 * Simplified DialogUtils to resolve compilation errors
 * This is a temporary solution until all dependencies are properly integrated
 */
public class DialogUtils {

    private static DialogUtils instance;

    public static DialogUtils getInstance() {
        if (instance == null) {
            instance = new DialogUtils();
        }
        return instance;
    }

    public MaterialDialog.Builder createCustomDialogWithoutContent(Context context, int titleRes) {
        return new MaterialDialog.Builder();
    }

    public MaterialDialog.Builder createAnimationDialog(Context context) {
        return new MaterialDialog.Builder();
    }

    public MaterialDialog.Builder createOverwriteDialog(Context context) {
        return new MaterialDialog.Builder();
    }
}
