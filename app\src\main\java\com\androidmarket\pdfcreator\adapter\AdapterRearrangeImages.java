package com.androidmarket.pdfcreator.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.squareup.picasso.Picasso;

import java.io.File;
import java.util.ArrayList;

import com.androidmarket.pdfcreator.R;
import com.androidmarket.pdfcreator.databinding.ItemRearrangeImagesBinding;

public class AdapterRearrangeImages extends RecyclerView.Adapter<AdapterRearrangeImages.ViewHolder> {
    private ArrayList<String> mImagesUri;
    private final Context mContext;
    private final OnClickListener mOnClickListener;

    public AdapterRearrangeImages(OnClickListener onClickListener,
                                  ArrayList<String> uris, Context context) {
        mOnClickListener = onClickListener;
        mImagesUri = uris;
        mContext = context;
    }

    @NonNull
    @Override
    public AdapterRearrangeImages.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_rearrange_images, parent, false);
        return new AdapterRearrangeImages.ViewHolder(view);
    }

    @SuppressLint("NewApi")
    @Override
    public void onBindViewHolder(@NonNull AdapterRearrangeImages.ViewHolder holder, int position) {
        File imageFile = new File(mImagesUri.get(position));
        if (position == 0) {
            holder.binding.buttonUp.setVisibility(View.GONE);
        } else {
            holder.binding.buttonUp.setVisibility(View.VISIBLE);
        }
        if (position == getItemCount() - 1) {
            holder.binding.buttonDown.setVisibility(View.GONE);
        } else {
            holder.binding.buttonDown.setVisibility(View.VISIBLE);
        }
        Picasso.with(mContext).load(imageFile).into(holder.binding.image);
        holder.binding.pageNumber.setText(String.valueOf(position + 1));
    }

    @Override
    public int getItemCount() {
        return mImagesUri.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private final ItemRearrangeImagesBinding binding;

        ViewHolder(View itemView) {
            super(itemView);
            binding = ItemRearrangeImagesBinding.bind(itemView);

            binding.buttonDown.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mOnClickListener.onDownClick(getBindingAdapterPosition());
                }
            });

            binding.buttonUp.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mOnClickListener.onUpClick(getBindingAdapterPosition());
                }
            });

            binding.removeImage.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mOnClickListener.onRemoveClick(getBindingAdapterPosition());
                }
            });
        }
    }

    public void positionChanged(ArrayList<String> images) {
        mImagesUri = images;
        notifyDataSetChanged();
    }

    public interface OnClickListener {
        void onUpClick(int position);
        void onDownClick(int position);
        void onRemoveClick(int position);
    }
}