apply plugin: 'com.android.application'

android {
    compileSdk 35
    namespace 'com.androidmarket.pdfcreator'
    defaultConfig {
        applicationId "com.androidmarket.pdfcreator"
        vectorDrawables.useSupportLibrary = true
        minSdkVersion 26
        multiDexEnabled true
        targetSdkVersion 34
        versionCode 108
        versionName "8.8.1"
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            debuggable true
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/LICENSE.txt'
    }
    lintOptions {
        checkReleaseBuilds false
    }
    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
    }
    viewBinding {
        enabled = true
    }
    dataBinding {
        enabled = true
    }
    buildToolsVersion '35.0.1'
    ndkVersion '23.1.7779620'
}
configurations {
    all {
        exclude module: 'httpclient'
        exclude group: 'org.apache.poi', module: 'poi-ooxml-lite'
    }
}
repositories {
    mavenCentral()
    maven { url 'https://jitpack.io' }
    maven { url "https://repository.aspose.com/repo/" }
}
dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'junit:junit:4.13.2'
    implementation 'androidx.vectordrawable:vectordrawable-animated:1.2.0'
    implementation 'androidx.exifinterface:exifinterface:1.4.1'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'androidx.legacy:legacy-support-v13:1.0.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    implementation 'androidx.annotation:annotation:1.6.0'

    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.preference:preference:1.2.1'

    // Removed ButterKnife dependencies
    // تم إزالة التبعيات الخاصة بـ ButterKnife نهائيًا

    // persistence room library - for db operations
    implementation 'androidx.room:room-runtime:2.7.1'
    annotationProcessor 'androidx.room:room-compiler:2.7.1'

    // material ripple, morphing button, material dialog, animations
    implementation 'com.balysv:material-ripple:1.0.2'
    implementation 'com.github.dmytrodanylyk:android-morphing-button:98a4986e56'
    implementation 'com.afollestad.material-dialogs:core:3.3.0'
    implementation 'com.airbnb.android:lottie:6.6.6'

    // libraries for reading from doc and docx files
    implementation group: 'org.apache.xmlbeans', name: 'xmlbeans', version: '5.3.0'
    implementation group: 'org.apache.poi', name: 'poi', version: '5.4.1'
    implementation group: 'org.apache.poi', name: 'poi-ooxml', version: '5.4.1'
    implementation group: 'org.apache.poi', name: 'poi-ooxml-schemas', version: '4.1.2'
    implementation group: 'org.apache.poi', name: 'poi-scratchpad', version: '5.4.1'

    // Itext pdf library
    implementation 'com.itextpdf:itextg:5.5.10'
    implementation 'com.madgag.spongycastle:core:********'
    implementation 'com.itextpdf:itext7-core:7.2.3'

    // Picasso, image editor, image cropper
    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation 'jp.wasabeef:picasso-transformations:2.4.0'
    // Remove the remote dependency for PhotoEditor - temporarily disabled
    // implementation 'com.github.burhanrashid52:PhotoEditor:1.0.0'
    // Add the local PhotoEditor module as a dependency - temporarily disabled
    // implementation project(':PhotoEditor')
    implementation 'com.github.CanHub:Android-Image-Cropper:4.3.3' // استخدام إصدار أقدم من Android Image Cropper

    // Update the dependency to use the available version of Android Viewpager Transformers
    implementation 'com.eftimoff:android-viewpager-transformers:1.0.1'

    // Image picker, and folder picker
    implementation 'com.github.zhihu:Matisse:0.5.2' // استخدام إصدار محدد من مكتبة Matisse عبر jitpack.io

    // Zxing - for scanning qr code
    implementation 'com.google.zxing:core:3.5.3'
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'

    // color picker
    implementation 'com.github.danielnilsson9:color-picker-view:master'

    //admob
    implementation 'com.google.android.gms:play-services-ads:24.2.0'

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    testImplementation 'org.mockito:mockito-core:5.17.0'
    testImplementation 'org.robolectric:robolectric:4.14.1'

    androidTestImplementation('androidx.test.espresso:espresso-core:3.6.1', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })

    implementation 'com.github.bumptech.glide:glide:4.15.1'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.15.1'

    // إضافة مكتبة PhotoFilter إذا كانت مستخدمة

    // إزالة مكتبة ButterKnife
    // implementation 'com.jakewharton:butterknife:10.2.3'
    // annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'

    // مكتبة Google Ads - تم إزالة التكرار
    // implementation 'com.google.android.gms:play-services-ads:21.5.0'

    // مكتبة Material Dialogs - تم إزالة التكرار
    // implementation 'com.afollestad.material-dialogs:core:3.3.0'

    // مكتبة PhotoFilter (تأكد من وجودها في مستودعك أو أضفها يدويًا إذا كانت مكتبة محلية)
    implementation 'com.github.zomato:androidphotofilters:1.0.1'

    // إضافة التبعيات اللازمة - تم إزالة التكرار
    // implementation 'androidx.appcompat:appcompat:1.6.1'
    // implementation 'androidx.preference:preference:1.2.0'
    // implementation 'androidx.databinding:viewbinding:7.0.0'

    // استخدام مكتبة أخرى للـ image cropping
    // implementation 'com.github.theartofdev.edmodo:android-image-cropper:2.8.0' // تحديد إصدار ثابت

    // ButterKnife for view binding - disabled due to Java compatibility issues
    // implementation 'com.jakewharton:butterknife:10.2.3'
    // annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'

    // Material Dialogs
    implementation 'com.afollestad.material-dialogs:core:3.3.0'

    // Google Ads
    implementation 'com.google.android.gms:play-services-ads:21.5.0'
}

tasks.withType(JavaCompile) {
    options.compilerArgs -= [
        '--add-exports', 'jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED'
    ]
    options.compilerArgs += [
        '--add-exports', 'jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED',
        '--add-exports', 'jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED'
    ]
}

assemble.dependsOn('lint')
