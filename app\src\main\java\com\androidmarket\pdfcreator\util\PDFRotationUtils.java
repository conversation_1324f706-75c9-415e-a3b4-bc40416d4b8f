package com.androidmarket.pdfcreator.util;

import android.content.Context;

/**
 * Simplified PDFRotationUtils to resolve compilation errors
 * This is a temporary solution until all dependencies are properly integrated
 */
public class PDFRotationUtils {

    private Context mContext;

    public PDFRotationUtils(Context context) {
        this.mContext = context;
    }

    public void rotatePages(String filePath, Object callback) {
        // Simplified implementation
    }
}
