package com.androidmarket.pdfcreator.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import java.util.ArrayList;

import com.androidmarket.pdfcreator.R;
import com.androidmarket.pdfcreator.databinding.ItemRearrangeImagesBinding;

public class AdapterRearrangePdf extends RecyclerView.Adapter<AdapterRearrangePdf.ViewHolder> {
    private ArrayList<Bitmap> mBitmaps;
    private final Context mContext;
    private final OnClickListener mOnClickListener;

    public AdapterRearrangePdf(OnClickListener onClickListener,
                               ArrayList<Bitmap> uris, Context context) {
        mOnClickListener = onClickListener;
        mBitmaps = uris;
        mContext = context;
    }

    @NonNull
    @Override
    public AdapterRearrangePdf.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemRearrangeImagesBinding binding = ItemRearrangeImagesBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new AdapterRearrangePdf.ViewHolder(binding);
    }

    @SuppressLint("NewApi")
    @Override
    public void onBindViewHolder(@NonNull AdapterRearrangePdf.ViewHolder holder, int position) {
        if (position == 0) {
            holder.binding.buttonUp.setVisibility(View.GONE);
        } else {
            holder.binding.buttonUp.setVisibility(View.VISIBLE);
        }
        if (position == getItemCount() - 1) {
            holder.binding.buttonDown.setVisibility(View.GONE);
        } else {
            holder.binding.buttonDown.setVisibility(View.VISIBLE);
        }
        holder.binding.image.setImageBitmap(mBitmaps.get(position));
        holder.binding.pageNumber.setText(String.valueOf(position + 1));
    }

    @Override
    public int getItemCount() {
        return mBitmaps.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        private final ItemRearrangeImagesBinding binding;

        ViewHolder(ItemRearrangeImagesBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
            binding.buttonDown.setOnClickListener(this);
            binding.buttonUp.setOnClickListener(this);
            binding.removeImage.setOnClickListener(this);
        }

        @Override
        public void onClick(View view) {
            switch (view.getId()) {
                case R.id.buttonUp:
                    mOnClickListener.onUpClick(getAdapterPosition());
                    break;
                case R.id.buttonDown:
                    mOnClickListener.onDownClick(getAdapterPosition());
                    break;
                case R.id.removeImage:
                    mOnClickListener.onRemoveClick(getAdapterPosition());
            }
        }
    }

    public void positionChanged(ArrayList<Bitmap> images) {
        mBitmaps = images;
        notifyDataSetChanged();
    }

    public interface OnClickListener {
        void onUpClick(int position);
        void onDownClick(int position);
        void onRemoveClick(int position);
    }
}