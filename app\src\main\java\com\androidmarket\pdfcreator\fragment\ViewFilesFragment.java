package com.androidmarket.pdfcreator.fragment;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.androidmarket.pdfcreator.adapter.AdapterViewFiles;
import com.androidmarket.pdfcreator.interfaces.EmptyStateChangeListener;
import com.androidmarket.pdfcreator.interfaces.ItemSelectedListener;
import com.androidmarket.pdfcreator.util.FileSortUtils;
import com.androidmarket.pdfcreator.util.ViewFilesDividerItemDecoration;

import static android.preference.PreferenceManager.getDefaultSharedPreferences;
import static com.androidmarket.pdfcreator.Constants.SORTING_INDEX;

public class ViewFilesFragment extends Fragment
        implements SwipeRefreshLayout.OnRefreshListener,
        EmptyStateChangeListener,
        ItemSelectedListener {

    private SharedPreferences mSharedPreferences;
    private AdapterViewFiles mAdapterViewFiles;
    private int mCurrentSortingIndex;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {


        // Initialize variables
        mSharedPreferences = getDefaultSharedPreferences(requireActivity());
        mCurrentSortingIndex = mSharedPreferences.getInt(SORTING_INDEX, FileSortUtils.getInstance().NAME_INDEX);
        mAdapterViewFiles = new AdapterViewFiles(requireActivity(), null, this, this);

        RecyclerView.LayoutManager mLayoutManager = new LinearLayoutManager(root.getContext());

            startActivity(new Intent(getActivity(), HomeActivity.class));
            requireActivity().finish();
        });

        return root;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}