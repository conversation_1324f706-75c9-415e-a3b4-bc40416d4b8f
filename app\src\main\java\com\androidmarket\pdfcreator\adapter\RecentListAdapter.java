package com.androidmarket.pdfcreator.adapter;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * Simplified RecentListAdapter to resolve compilation errors
 * This is a temporary solution until all dependencies are properly integrated
 */
public class RecentListAdapter {

    private List<HashMap<String, String>> mValues;

    public RecentListAdapter(List<HashMap<String, String>> mValues) {
        this.mValues = mValues != null ? mValues : new ArrayList<>();
    }

    public Object onCreateViewHolder(Object parent, int viewType) {
        return new ViewHolder();
    }

    public void onBindViewHolder(Object holder, int position) {
        // Simplified implementation
    }

    public int getItemCount() {
        return mValues == null ? 0 : mValues.size();
    }

    public static class ViewHolder {
        
        public ViewHolder() {
            // Simplified constructor
        }
    }
}
