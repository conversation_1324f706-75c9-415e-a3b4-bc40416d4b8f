package com.androidmarket.pdfcreator.adapter;

import androidx.annotation.NonNull;
import com.google.android.material.card.MaterialCardView;

import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import java.util.List;
import java.util.Map;

import com.androidmarket.pdfcreator.databinding.ItemViewEnhancementOptionBinding;

import com.androidmarket.pdfcreator.R;

public class RecentListAdapter extends RecyclerView.Adapter<RecentListAdapter.RecentItemViewHolder>  {

    private List<String> mKeys;
    private List<Map<String, String>> mValues;
    private final View.OnClickListener mOnClickListener;

    public RecentListAdapter(View.OnClickListener listener) {
        this.mOnClickListener = listener;
    }


    /**
     * Updates the recent list
     * @param keys - list of all the feature viewId
     * @param recentList - list of the features
     * */
    public void updateList(List<String> keys, List<Map<String, String>> recentList) {
        this.mKeys = keys;
        this.mValues = recentList;
    }

    @NonNull @Override public RecentItemViewHolder onCreateViewHolder(
            @NonNull final ViewGroup viewGroup, final int i) {
        ItemViewEnhancementOptionBinding binding = ItemViewEnhancementOptionBinding.inflate(
                LayoutInflater.from(viewGroup.getContext()), viewGroup, false);
        return new RecentItemViewHolder(binding);
    }

    @Override public void onBindViewHolder(
            @NonNull final RecentItemViewHolder recentItemViewHolder, final int i) {

        recentItemViewHolder.binding.optionName.setText(recentItemViewHolder.itemView.getContext().getString(
                Integer.parseInt(mValues.get(i).keySet().iterator().next())
        ));
        recentItemViewHolder.binding.optionImage.setImageDrawable(ContextCompat.getDrawable(recentItemViewHolder.itemView.getContext(),
                Integer.parseInt(mValues.get(i).values().iterator().next())));

        recentItemViewHolder.binding.getRoot().setId(Integer.parseInt(mKeys.get(i)));
        recentItemViewHolder.binding.getRoot().setOnClickListener(mOnClickListener);
    }

    @Override public int getItemCount() {
        return mValues.size();
    }

    class RecentItemViewHolder extends RecyclerView.ViewHolder {
        private final ItemViewEnhancementOptionBinding binding;

        private RecentItemViewHolder(@NonNull final ItemViewEnhancementOptionBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }
}
