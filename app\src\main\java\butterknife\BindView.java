package butterknife;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Stub annotation for BindView to resolve compilation errors
 * This is a temporary solution until ButterKnife is properly integrated or replaced
 */
@Retention(RUNTIME)
@Target(FIELD)
public @interface BindView {
    int value();
}
