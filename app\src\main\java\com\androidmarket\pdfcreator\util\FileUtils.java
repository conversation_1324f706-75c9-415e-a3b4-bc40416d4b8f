package com.androidmarket.pdfcreator.util;

import android.content.Context;
import java.util.function.Consumer;

/**
 * Simplified FileUtils to resolve compilation errors
 * This is a temporary solution until all dependencies are properly integrated
 */
public class FileUtils {

    private static FileUtils instance;

    public static FileUtils getInstance() {
        if (instance == null) {
            instance = new FileUtils(null);
        }
        return instance;
    }

    public void openSaveDialog(String fileName, Consumer<String> saveMethod, Context context) {
        // Simplified implementation
        if (saveMethod != null) {
            saveMethod.accept(fileName);
        }
    }

    public boolean isFileExist(String path) {
        // Simplified implementation
        return false;
    }

    public void deleteFile(String path) {
        // Simplified implementation
    }

    public FileUtils(Context context) {
        // Constructor for compatibility
    }

    public static String getFileName(String path) {
        if (path == null) return "";
        int lastSlash = path.lastIndexOf('/');
        return lastSlash >= 0 ? path.substring(lastSlash + 1) : path;
    }

    public static String getFileNameWithoutExtension(String path) {
        String fileName = getFileName(path);
        int lastDot = fileName.lastIndexOf('.');
        return lastDot >= 0 ? fileName.substring(0, lastDot) : fileName;
    }

    public enum FileType {
        e_PDF
    }

    public Object openFile(String path, FileType type) {
        // Simplified implementation
        return new Object();
    }
}
