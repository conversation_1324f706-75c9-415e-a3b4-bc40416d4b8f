package com.androidmarket.pdfcreator.adapter;

import android.app.Activity;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import java.io.File;
import java.util.HashMap;
import java.util.List;

import com.androidmarket.pdfcreator.R;
import com.androidmarket.pdfcreator.db.History;

public class AdapterHistory extends RecyclerView.Adapter<AdapterHistory.ViewHistoryHolder> {

    private final List<History> mHistoryList;
    private final Activity mActivity;
    private final OnClickListener mOnClickListener;
    private final HashMap<String, Integer> mIconsOperationList;

    public AdapterHistory(Activity mActivity, List<History> mHistoryList, OnClickListener mOnClickListener) {
        this.mHistoryList = mHistoryList;
        this.mActivity = mActivity;
        this.mOnClickListener = mOnClickListener;
        mIconsOperationList = new HashMap<>();
        mIconsOperationList.put(mActivity.getString(R.string.printed), R.drawable.ic_print_black_24dp);
        mIconsOperationList.put(mActivity.getString(R.string.created), R.drawable.ic_insert_drive_file_black_24dp);
        mIconsOperationList.put(mActivity.getString(R.string.deleted), R.drawable.baseline_delete_24);
        mIconsOperationList.put(mActivity.getString(R.string.renamed), R.drawable.ic_create_black_24dp);
        mIconsOperationList.put(mActivity.getString(R.string.rotated), R.drawable.baseline_crop_rotate_24);
        mIconsOperationList.put(mActivity.getString(R.string.encrypted), R.drawable.ic_lock_black_24dp);
        mIconsOperationList.put(mActivity.getString(R.string.decrypted), R.drawable.ic_lock_open_black_24dp);
    }

    @NonNull
    @Override
    public ViewHistoryHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        return new ViewHistoryHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHistoryHolder holder, int position) {

        final String filePath = mHistoryList.get(position).getFilePath();
        File file = new File(filePath);
        final String operationDate = mHistoryList.get(position).getDate();
        String[] formatDate = operationDate.split(" ");
        String date;
        if (formatDate.length >= 3) {
            String time = formatDate[3];
            String[] formatTime =  time.split(":");
            date = formatTime[0] + ":" + formatTime[1];
            date = formatDate[0] + ", " + formatDate[1] + " " + formatDate[2] + " at " + date;
        } else {
            date = operationDate;
        }

        final String operationType = mHistoryList.get(position).getOperationType();
        final String fileName = file.getName();

        if (mIconsOperationList != null && mIconsOperationList.containsKey(operationType))
        else
    }

    public void deleteHistory() {
        mHistoryList.clear();
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return mHistoryList == null ? 0 : mHistoryList.size();
    }

    public static class ViewHistoryHolder extends RecyclerView.ViewHolder {

            this.binding = binding;
        }
    }

    public interface OnClickListener {
        void onItemClick(String path);
    }
}