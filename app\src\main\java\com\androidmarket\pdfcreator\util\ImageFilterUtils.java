package com.androidmarket.pdfcreator.util;

import android.content.Context;

import java.util.ArrayList;

import com.androidmarket.pdfcreator.R;
import com.androidmarket.pdfcreator.pdfModel.FilterItem;

public class ImageFilterUtils {

    public ImageFilterUtils() {
    }

    private static class SingletonHolder {
        static final ImageFilterUtils INSTANCE = new ImageFilterUtils();
    }

    public static ImageFilterUtils getInstance() {
        return ImageFilterUtils.SingletonHolder.INSTANCE;
    }

    public ArrayList<FilterItem> getFiltersList(Context context) {

        ArrayList<FilterItem> items = new ArrayList<>();

        // Simplified implementation - using null instead of PhotoFilter
        items.add(new FilterItem(0, "None", null));
        items.add(new FilterItem(0, "Brush", null));
        items.add(new FilterItem(0, "Auto Fix", null));
        items.add(new FilterItem(0, "Gray<PERSON>le", null));
        items.add(new FilterItem(0, "Brightness", null));
        items.add(new FilterItem(0, "Contrast", null));

        return items;
    }
}
