package com.androidmarket.pdfcreator.util;

import android.content.Context;
import android.widget.ImageView;

/**
 * Simplified Picasso to resolve compilation errors
 * This is a temporary solution until all dependencies are properly integrated
 */
public class Picasso {

    public static Picasso with(Context context) {
        return new Picasso();
    }

    public Picasso load(String path) {
        return this;
    }

    public void into(ImageView imageView) {
        // Simplified implementation
    }
}
