package com.androidmarket.pdfcreator.adapter;

import android.content.Context;
import java.util.ArrayList;

/**
 * Simplified AdapterExtractImages to resolve compilation errors
 * This is a temporary solution until all dependencies are properly integrated
 */
public class AdapterExtractImages {

    private final Context mContext;
    private final ArrayList<String> mFilePaths;

    public AdapterExtractImages(Context context, ArrayList<String> filePaths) {
        mContext = context;
        mFilePaths = filePaths != null ? filePaths : new ArrayList<>();
    }

    public AdapterExtractImages(Context context, ArrayList<String> filePaths, OnFileItemClickedListener listener) {
        mContext = context;
        mFilePaths = filePaths != null ? filePaths : new ArrayList<>();
        // Store listener if needed
    }

    public Object onCreateViewHolder(Object parent, int viewType) {
        // Simplified implementation
        return new Object();
    }

    public void onBindViewHolder(Object holder, int position) {
        // Simplified implementation
    }

    public int getItemCount() {
        return mFilePaths.size();
    }

    public interface OnFileItemClickedListener {
        void onFileItemClick(String path);
    }
}
