package com.afollestad.materialdialogs;

/**
 * Stub class for MaterialDialog to resolve compilation errors
 * This is a temporary solution until the Material Dialogs library is properly integrated
 */
public class MaterialDialog {
    
    public static class Builder {
        public Builder(Object context) {
            // Stub constructor
        }
        
        public Builder title(String title) {
            return this;
        }
        
        public Builder title(int titleRes) {
            return this;
        }
        
        public Builder content(String content) {
            return this;
        }
        
        public Builder content(int contentRes) {
            return this;
        }
        
        public Builder positiveText(String text) {
            return this;
        }
        
        public Builder positiveText(int textRes) {
            return this;
        }
        
        public Builder negativeText(String text) {
            return this;
        }
        
        public Builder negativeText(int textRes) {
            return this;
        }
        
        public Builder neutralText(String text) {
            return this;
        }
        
        public Builder neutralText(int textRes) {
            return this;
        }
        
        public MaterialDialog build() {
            return new MaterialDialog();
        }
        
        public MaterialDialog show() {
            return new MaterialDialog();
        }
    }
    
    public void show() {
        // Stub implementation
    }
    
    public void dismiss() {
        // Stub implementation
    }
}
