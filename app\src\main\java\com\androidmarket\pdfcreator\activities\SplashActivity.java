package com.androidmarket.pdfcreator.activities;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import androidx.appcompat.app.AppCompatActivity;
import com.androidmarket.pdfcreator.R;
import com.androidmarket.pdfcreator.util.AdsUtility;

/**
 * Simplified SplashActivity to resolve compilation errors
 * This is a temporary solution until all dependencies are properly integrated
 */
public class SplashActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        AdsUtility.loadInterstitialAd(this);

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                // Simplified implementation - just start HomeActivity
                startActivity(new Intent(SplashActivity.this, HomeActivity.class));
                finish();
            }
        }, 3000);
    }
}
