package com.androidmarket.pdfcreator.util;

import android.content.Context;
import java.util.ArrayList;

/**
 * Simplified CommonCodeUtils to resolve compilation errors
 * This is a temporary solution until all dependencies are properly integrated
 */
public class CommonCodeUtils {

    private static CommonCodeUtils instance;

    public static CommonCodeUtils getInstance() {
        if (instance == null) {
            instance = new CommonCodeUtils();
        }
        return instance;
    }

    public void populateUtil(Context context, ArrayList<String> outputFilePaths, Object listener, Object recyclerView) {
        // Simplified implementation
    }

    public void closePopup() {
        // Simplified implementation
    }
}
