package com.androidmarket.pdfcreator.util;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.androidmarket.pdfcreator.R;

/**
 * <AUTHOR> (<EMAIL>)
 *         Created on 29.10.17.
 *         Item decoration which draws a simple dividing line between elements
 */

public class ViewFilesDividerItemDecoration extends RecyclerView.ItemDecoration {
    private final Drawable mDivider;

    public ViewFilesDividerItemDecoration(Context context) {
        mDivider = context.getResources().getDrawable(R.drawable.files_divider);
    }

    @Override
    public void onDrawOver(@NonNull Canvas canvas, RecyclerView parent, @NonNull RecyclerView.State state) {
        int left = parent.getPaddingLeft();
        int right = parent.getWidth() - parent.getPaddingRight();

        int childCount = parent.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View child = parent.getChildAt(i);

            RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) child.getLayoutParams();

            int top = child.getBottom() + params.bottomMargin;
            int bottom = top + mDivider.getIntrinsicHeight();

            mDivider.setBounds(left, top, right, bottom);
            mDivider.draw(canvas);
        }
    }
}
