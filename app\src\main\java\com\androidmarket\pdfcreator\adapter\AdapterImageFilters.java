package com.androidmarket.pdfcreator.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import java.util.ArrayList;

import com.androidmarket.pdfcreator.R;
import com.androidmarket.pdfcreator.interfaces.OnFilterItemClickedListener;
import com.androidmarket.pdfcreator.pdfModel.FilterItem;
import com.androidmarket.pdfcreator.util.ImageUtils;
import android.view.View;
import androidx.recyclerview.widget.RecyclerView;

public class AdapterImageFilters extends RecyclerView.Adapter<AdapterImageFilters.ViewHolder> {

    private final ArrayList<FilterItem> mFilterItem;
    private final OnFilterItemClickedListener mOnFilterItemClickedListener;
    private final Context  mContext;

    public AdapterImageFilters(ArrayList<FilterItem> filterItems, Context context,
                               OnFilterItemClickedListener listener) {
        mFilterItem = filterItems;
        mContext = context;
        mOnFilterItemClickedListener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.list_item_filter, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        int imageId = mFilterItem.get(position).getImageId();
        Bitmap roundBitmap = BitmapFactory.decodeResource(mContext.getResources(), imageId);
        if (roundBitmap != null) {
            holder.img.setImageBitmap(ImageUtils.getInstance().getRoundBitmap(roundBitmap));
        } else
            holder.img.setImageResource(imageId);
        holder.name.setText(mFilterItem.get(position).getName());
    }

    @Override
    public int getItemCount() {
        return mFilterItem.size();
    }

    // Fix leaking 'this' issue by using an explicit OnClickListener
    public class ViewHolder extends RecyclerView.ViewHolder {
        ImageView img;
        TextView name;

        ViewHolder(View itemView) {
            super(itemView);
            img = itemView.findViewById(R.id.filter_preview);
            name = itemView.findViewById(R.id.filter_Name);
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    int position = getBindingAdapterPosition();
                    if (position != RecyclerView.NO_POSITION && mOnFilterItemClickedListener != null) {
                        mOnFilterItemClickedListener.onItemClick(v, position);
                    }
                }
            });
        }
    }
}
