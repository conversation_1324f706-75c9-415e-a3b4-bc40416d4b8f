package butterknife;

import android.app.Activity;
import android.view.View;

/**
 * Stub class for ButterKnife to resolve compilation errors
 * This is a temporary solution until ButterKnife is properly integrated or replaced
 */
public class ButterKnife {
    
    public static void bind(Activity target) {
        // Stub implementation - does nothing
    }
    
    public static void bind(Object target, View source) {
        // Stub implementation - does nothing
    }
    
    public static void bind(Object target, Activity source) {
        // Stub implementation - does nothing
    }
}
